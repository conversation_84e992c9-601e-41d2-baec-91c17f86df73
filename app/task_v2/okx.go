package task_v2

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"bepusdt/app/task/rate"
)

// initRateTasks 初始化汇率任务
func initRateTasks(ctx *TaskContext) error {
	// 注册OKX汇率获取任务
	registerTask(task{callback: func(taskCtx context.Context) {
		okxRateUpdate(ctx, taskCtx)
	}, duration: time.Minute * 1}) // 每分钟更新一次汇率

	ctx.LogInfo("Rate tasks initialized")
	return nil
}

// okxRateUpdate OKX汇率更新
func okxRateUpdate(ctx *TaskContext, taskCtx context.Context) {
	// 更新USDT汇率
	if err := updateUSDTRate(ctx); err != nil {
		ctx.ReportError(ErrorTypeNetwork, "okxRateUpdate", "", err, map[string]interface{}{
			"currency": "USDT",
		})
	}

	// 更新USDC汇率
	if err := updateUSDCRate(ctx); err != nil {
		ctx.ReportError(ErrorTypeNetwork, "okxRateUpdate", "", err, map[string]interface{}{
			"currency": "USDC",
		})
	}

	// 更新TRX汇率
	if err := updateTRXRate(ctx); err != nil {
		ctx.ReportError(ErrorTypeNetwork, "okxRateUpdate", "", err, map[string]interface{}{
			"currency": "TRX",
		})
	}

	ctx.ReportSuccess("okxRateUpdate", "", map[string]interface{}{
		"currencies": []string{"USDT", "USDC", "TRX"},
	})
}

// updateUSDTRate 更新USDT汇率
func updateUSDTRate(ctx *TaskContext) error {
	rate, err := fetchOKXRate("USDT-CNY")
	if err != nil {
		return fmt.Errorf("failed to fetch USDT rate: %w", err)
	}

	// 更新全局汇率
	if err := rate.SetOkxUsdtRawRate(rate); err != nil {
		return fmt.Errorf("failed to set USDT rate: %w", err)
	}

	ctx.LogInfo("USDT rate updated", "rate", rate)
	return nil
}

// updateUSDCRate 更新USDC汇率
func updateUSDCRate(ctx *TaskContext) error {
	rate, err := fetchOKXRate("USDC-CNY")
	if err != nil {
		return fmt.Errorf("failed to fetch USDC rate: %w", err)
	}

	// 更新全局汇率
	if err := rate.SetOkxUsdcRawRate(rate); err != nil {
		return fmt.Errorf("failed to set USDC rate: %w", err)
	}

	ctx.LogInfo("USDC rate updated", "rate", rate)
	return nil
}

// updateTRXRate 更新TRX汇率
func updateTRXRate(ctx *TaskContext) error {
	rate, err := fetchOKXRate("TRX-CNY")
	if err != nil {
		return fmt.Errorf("failed to fetch TRX rate: %w", err)
	}

	// 更新全局汇率
	if err := rate.SetOkxTrxRawRate(rate); err != nil {
		return fmt.Errorf("failed to set TRX rate: %w", err)
	}

	ctx.LogInfo("TRX rate updated", "rate", rate)
	return nil
}

// fetchOKXRate 从OKX获取汇率
func fetchOKXRate(symbol string) (float64, error) {
	url := fmt.Sprintf("https://www.okx.com/api/v5/market/ticker?instId=%s", symbol)
	
	client := &http.Client{
		Timeout: time.Second * 10,
	}

	resp, err := client.Get(url)
	if err != nil {
		return 0, fmt.Errorf("failed to request OKX API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("OKX API returned status code: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, fmt.Errorf("failed to read response body: %w", err)
	}

	var response OKXResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return 0, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	if response.Code != "0" {
		return 0, fmt.Errorf("OKX API error: %s - %s", response.Code, response.Msg)
	}

	if len(response.Data) == 0 {
		return 0, fmt.Errorf("no data returned from OKX API")
	}

	rate, err := parseFloat(response.Data[0].Last)
	if err != nil {
		return 0, fmt.Errorf("failed to parse rate: %w", err)
	}

	return rate, nil
}

// OKXResponse OKX API响应结构
type OKXResponse struct {
	Code string    `json:"code"`
	Msg  string    `json:"msg"`
	Data []OKXData `json:"data"`
}

// OKXData OKX数据结构
type OKXData struct {
	InstType  string `json:"instType"`
	InstId    string `json:"instId"`
	Last      string `json:"last"`
	LastSz    string `json:"lastSz"`
	AskPx     string `json:"askPx"`
	AskSz     string `json:"askSz"`
	BidPx     string `json:"bidPx"`
	BidSz     string `json:"bidSz"`
	Open24h   string `json:"open24h"`
	High24h   string `json:"high24h"`
	Low24h    string `json:"low24h"`
	Vol24h    string `json:"vol24h"`
	VolCcy24h string `json:"volCcy24h"`
	Ts        string `json:"ts"`
}

// parseFloat 解析浮点数
func parseFloat(s string) (float64, error) {
	var f float64
	if err := json.Unmarshal([]byte(s), &f); err != nil {
		return 0, err
	}
	return f, nil
}

// getRateWithFallback 获取汇率，失败时使用默认值
func getRateWithFallback(ctx *TaskContext, symbol string, defaultRate float64) float64 {
	rate, err := fetchOKXRate(symbol)
	if err != nil {
		ctx.LogWarn("Failed to fetch rate, using default",
			"symbol", symbol,
			"default_rate", defaultRate,
			"error", err.Error())
		return defaultRate
	}
	return rate
}

// validateRate 验证汇率是否合理
func validateRate(rate float64, symbol string) error {
	if rate <= 0 {
		return fmt.Errorf("invalid rate for %s: %f", symbol, rate)
	}

	// 设置合理的汇率范围
	var minRate, maxRate float64
	switch symbol {
	case "USDT-CNY", "USDC-CNY":
		minRate, maxRate = 5.0, 10.0 // USDT/USDC 合理范围 5-10 CNY
	case "TRX-CNY":
		minRate, maxRate = 0.1, 2.0 // TRX 合理范围 0.1-2 CNY
	default:
		return nil // 未知符号，跳过验证
	}

	if rate < minRate || rate > maxRate {
		return fmt.Errorf("rate %f for %s is out of reasonable range [%f, %f]", 
			rate, symbol, minRate, maxRate)
	}

	return nil
}

// updateRateWithValidation 带验证的汇率更新
func updateRateWithValidation(ctx *TaskContext, symbol string, updateFunc func(float64) error) error {
	rate, err := fetchOKXRate(symbol)
	if err != nil {
		return fmt.Errorf("failed to fetch %s rate: %w", symbol, err)
	}

	if err := validateRate(rate, symbol); err != nil {
		return fmt.Errorf("rate validation failed for %s: %w", symbol, err)
	}

	if err := updateFunc(rate); err != nil {
		return fmt.Errorf("failed to update %s rate: %w", symbol, err)
	}

	ctx.LogInfo("Rate updated successfully", "symbol", symbol, "rate", rate)
	return nil
}

// getRateHistory 获取汇率历史（占位符实现）
func getRateHistory(ctx *TaskContext, symbol string, days int) ([]RateHistory, error) {
	// 这里可以实现汇率历史记录功能
	// 暂时返回空切片
	return []RateHistory{}, nil
}

// RateHistory 汇率历史记录
type RateHistory struct {
	Symbol    string    `json:"symbol"`
	Rate      float64   `json:"rate"`
	Timestamp time.Time `json:"timestamp"`
}

// calculateRateChange 计算汇率变化
func calculateRateChange(ctx *TaskContext, symbol string) (float64, error) {
	// 这里可以实现汇率变化计算
	// 暂时返回0
	return 0, nil
}

// sendRateAlert 发送汇率警报
func sendRateAlert(ctx *TaskContext, symbol string, rate, threshold float64) error {
	if rate > threshold {
		message := fmt.Sprintf("汇率警报: %s 当前汇率 %.4f 超过阈值 %.4f", symbol, rate, threshold)
		ctx.LogWarn("Rate alert", "symbol", symbol, "rate", rate, "threshold", threshold)
		
		// 这里可以发送到Bot或其他通知渠道
		return sendBotAlert(ctx, "汇率警报", message)
	}
	return nil
}

// monitorRateChanges 监控汇率变化
func monitorRateChanges(ctx *TaskContext) error {
	symbols := []string{"USDT-CNY", "USDC-CNY", "TRX-CNY"}
	
	for _, symbol := range symbols {
		change, err := calculateRateChange(ctx, symbol)
		if err != nil {
			ctx.LogError("Failed to calculate rate change", "symbol", symbol, "error", err.Error())
			continue
		}

		// 如果变化超过5%，发送警报
		if change > 0.05 || change < -0.05 {
			message := fmt.Sprintf("汇率变化警报: %s 变化幅度 %.2f%%", symbol, change*100)
			if err := sendBotAlert(ctx, "汇率变化", message); err != nil {
				ctx.LogError("Failed to send rate change alert", "error", err.Error())
			}
		}
	}

	return nil
}

// getRateStatistics 获取汇率统计信息
func getRateStatistics(ctx *TaskContext) map[string]interface{} {
	return map[string]interface{}{
		"usdt_rate": rate.GetOkxUsdtRawRate(),
		"usdc_rate": rate.GetOkxUsdcRawRate(),
		"trx_rate":  rate.GetOkxTrxRawRate(),
		"last_update": time.Now().Format("2006-01-02 15:04:05"),
	}
}
