package task_v2

import (
	"context"
	"fmt"
	"time"

	"bepusdt/app/bot"
	"bepusdt/app/conf"
	"bepusdt/app/model"
)

// initBotTasks 初始化Bot任务
func initBotTasks(ctx *TaskContext) error {
	// 只有配置了Bot Token才启动相关任务
	if conf.BotToken() == "" {
		ctx.LogInfo("Bot token not configured, skipping bot tasks")
		return nil
	}

	// 注册Bot通知任务
	registerTask(task{callback: func(taskCtx context.Context) {
		botNotify(ctx, taskCtx)
	}, duration: time.Minute * 3})

	ctx.LogInfo("Bot tasks initialized", "admin_id", conf.BotAdminID())
	return nil
}

// botNotify Bot通知处理
func botNotify(ctx *TaskContext, taskCtx context.Context) {
	if conf.BotToken() == "" {
		return
	}

	// 获取需要发送Bot通知的订单
	orders := getBotPendingOrders()
	if len(orders) == 0 {
		return
	}

	ctx.LogInfo("Processing bot notifications", "count", len(orders))

	successCount := 0
	failedCount := 0

	for _, order := range orders {
		if err := sendBotNotification(ctx, &order); err != nil {
			ctx.ReportError(ErrorTypeNetwork, "botNotify", "", err, map[string]interface{}{
				"order_id": order.OrderId,
				"trade_id": order.TradeId,
			})
			failedCount++
		} else {
			successCount++
		}
	}

	ctx.ReportSuccess("botNotify", "", map[string]interface{}{
		"total_orders":  len(orders),
		"success_count": successCount,
		"failed_count":  failedCount,
	})
}

// getBotPendingOrders 获取需要发送Bot通知的订单
func getBotPendingOrders() []model.TradeOrders {
	var orders []model.TradeOrders

	// 获取状态为成功但还未发送过Bot通知的订单
	model.DB.Where("status = ? AND bot_notified = ?", 
		model.OrderStatusSuccess, false).Find(&orders)

	return orders
}

// sendBotNotification 发送Bot通知
func sendBotNotification(ctx *TaskContext, order *model.TradeOrders) error {
	// 构建Bot消息
	message := buildBotMessage(order)

	// 发送到管理员
	if err := bot.SendMessageToAdmin(message); err != nil {
		return fmt.Errorf("failed to send bot message to admin: %w", err)
	}

	// 如果配置了群组，也发送到群组
	if conf.BotNotifyTarget() != "" {
		if err := bot.SendMessageToGroup(conf.BotNotifyTarget(), message); err != nil {
			ctx.LogWarn("Failed to send bot message to group",
				"group_id", conf.BotNotifyTarget(),
				"error", err.Error())
			// 群组发送失败不影响整体流程
		}
	}

	// 更新Bot通知状态
	if err := updateBotNotificationStatus(order, true); err != nil {
		return fmt.Errorf("failed to update bot notification status: %w", err)
	}

	ctx.LogInfo("Bot notification sent",
		"order_id", order.OrderId,
		"admin_id", conf.BotAdminID())

	return nil
}

// buildBotMessage 构建Bot消息
func buildBotMessage(order *model.TradeOrders) string {
	emoji := order.GetStatusEmoji()
	statusLabel := order.GetStatusLabel()
	detailUrl := order.GetDetailUrl()

	message := fmt.Sprintf(`%s %s

💰 金额: %s %s
🏦 订单: %s
🔗 哈希: %s
📍 地址: %s
⏰ 时间: %s`,
		emoji, statusLabel,
		order.Amount, getTokenSymbol(order.TradeType),
		order.OrderId,
		order.TradeHash,
		order.Address,
		order.ConfirmedAt.Format("2006-01-02 15:04:05"))

	if detailUrl != "" {
		message += fmt.Sprintf("\n🔍 详情: %s", detailUrl)
	}

	return message
}

// getTokenSymbol 获取代币符号
func getTokenSymbol(tradeType string) string {
	switch {
	case contains(tradeType, "usdt"):
		return "USDT"
	case contains(tradeType, "usdc"):
		return "USDC"
	case contains(tradeType, "trx"):
		return "TRX"
	default:
		return "TOKEN"
	}
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[:len(substr)] == substr || 
		   len(s) > len(substr) && s[len(s)-len(substr):] == substr ||
		   (len(s) > len(substr) && findSubstring(s, substr))
}

// findSubstring 查找子字符串
func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// updateBotNotificationStatus 更新Bot通知状态
func updateBotNotificationStatus(order *model.TradeOrders, success bool) error {
	updates := map[string]interface{}{
		"bot_notified": true,
		"bot_notified_at": time.Now(),
	}

	if success {
		updates["bot_notify_success"] = true
	} else {
		updates["bot_notify_success"] = false
	}

	return model.DB.Model(order).Updates(updates).Error
}

// sendBotAlert 发送Bot警报
func sendBotAlert(ctx *TaskContext, alertType, message string) error {
	if conf.BotToken() == "" {
		return nil
	}

	alertMessage := fmt.Sprintf("🚨 系统警报\n\n类型: %s\n消息: %s\n时间: %s",
		alertType, message, time.Now().Format("2006-01-02 15:04:05"))

	if err := bot.SendMessageToAdmin(alertMessage); err != nil {
		return fmt.Errorf("failed to send bot alert: %w", err)
	}

	ctx.LogInfo("Bot alert sent", "type", alertType, "message", message)
	return nil
}

// sendBotSystemStatus 发送系统状态
func sendBotSystemStatus(ctx *TaskContext) error {
	if conf.BotToken() == "" {
		return nil
	}

	stats := GetSystemStats()
	
	message := fmt.Sprintf(`📊 系统状态报告

🔧 任务数量: %v
🌐 支持网络: %v
⚡ 系统状态: %s
📅 时间: %s`,
		stats["task_count"],
		len(GetSupportedNetworks()),
		getSystemHealthStatus(stats),
		time.Now().Format("2006-01-02 15:04:05"))

	// 添加队列状态
	if queues, ok := stats["queues"].(map[string]*QueueStats); ok {
		message += "\n\n📋 队列状态:"
		for name, queueStats := range queues {
			message += fmt.Sprintf("\n• %s: %d/%d/%d (总/成功/失败)",
				name, queueStats.TotalMessages, queueStats.ProcessedCount, queueStats.ErrorCount)
		}
	}

	if err := bot.SendMessageToAdmin(message); err != nil {
		return fmt.Errorf("failed to send system status: %w", err)
	}

	ctx.LogInfo("System status sent to bot")
	return nil
}

// getSystemHealthStatus 获取系统健康状态
func getSystemHealthStatus(stats map[string]interface{}) string {
	initialized, ok := stats["initialized"].(bool)
	if !ok || !initialized {
		return "❌ 未初始化"
	}

	taskCount, ok := stats["task_count"].(int)
	if !ok || taskCount == 0 {
		return "⚠️ 无任务运行"
	}

	return "✅ 正常运行"
}

// processBotCommand 处理Bot命令
func processBotCommand(ctx *TaskContext, command, userID string) error {
	// 检查用户权限
	if !isAuthorizedUser(userID) {
		return fmt.Errorf("unauthorized user: %s", userID)
	}

	switch command {
	case "/status":
		return sendBotSystemStatus(ctx)
	case "/stats":
		return sendBotStatistics(ctx)
	case "/help":
		return sendBotHelp(ctx)
	default:
		return fmt.Errorf("unknown command: %s", command)
	}
}

// isAuthorizedUser 检查用户是否有权限
func isAuthorizedUser(userID string) bool {
	adminID := fmt.Sprintf("%d", conf.BotAdminID())
	return userID == adminID
}

// sendBotStatistics 发送Bot统计信息
func sendBotStatistics(ctx *TaskContext) error {
	// 获取订单统计
	var totalOrders, successOrders, failedOrders int64
	
	model.DB.Model(&model.TradeOrders{}).Count(&totalOrders)
	model.DB.Model(&model.TradeOrders{}).Where("status = ?", model.OrderStatusSuccess).Count(&successOrders)
	model.DB.Model(&model.TradeOrders{}).Where("status = ?", model.OrderStatusFailed).Count(&failedOrders)

	message := fmt.Sprintf(`📈 系统统计

📊 订单统计:
• 总订单: %d
• 成功订单: %d
• 失败订单: %d
• 成功率: %.2f%%

⏰ 统计时间: %s`,
		totalOrders, successOrders, failedOrders,
		float64(successOrders)/float64(totalOrders)*100,
		time.Now().Format("2006-01-02 15:04:05"))

	if err := bot.SendMessageToAdmin(message); err != nil {
		return fmt.Errorf("failed to send statistics: %w", err)
	}

	return nil
}

// sendBotHelp 发送Bot帮助信息
func sendBotHelp(ctx *TaskContext) error {
	message := `🤖 Bot命令帮助

可用命令:
/status - 查看系统状态
/stats - 查看统计信息
/help - 显示此帮助信息

💡 提示: 只有管理员可以使用这些命令`

	if err := bot.SendMessageToAdmin(message); err != nil {
		return fmt.Errorf("failed to send help: %w", err)
	}

	return nil
}
